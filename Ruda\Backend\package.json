{"name": "backend", "version": "1.0.0", "description": "RUDA GeoAPI backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ruda", "g<PERSON><PERSON><PERSON>", "api", "postgresql"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.10", "pg": "^8.16.0"}}