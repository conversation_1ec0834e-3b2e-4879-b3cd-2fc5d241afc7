{"name": "ruda", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mapbox/mapbox-gl-draw": "^1.5.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.5.3", "@turf/turf": "^7.2.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "chart.js": "^4.5.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "mapbox-gl": "^3.12.0", "print-js": "^1.6.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-mui-sidebar": "^1.6.3", "react-router-dom": "^7.6.2", "react-sweet-progress": "^1.1.2", "reactstrap": "^9.2.3", "recharts": "^2.15.4", "x-data-grid": "^2.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^6.3.5"}}